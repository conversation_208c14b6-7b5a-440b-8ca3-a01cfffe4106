"""
測試版增強主程序
"""

# 添加路徑以支持模組導入
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime
import traceback

from agents.advanced_langchain_agent import AdvancedTradingAgent
from core.binance_api import BinanceAPI
import dotenv
dotenv.load_dotenv()


def test_trading_analysis():
    """測試交易分析功能"""
    try:
        print("🚀 測試增強版 LangChain 交易機器人")
        print("=" * 60)
        
        # 創建高級代理
        agent = AdvancedTradingAgent(
            model_name="gemini-2.5-flash-lite-preview-06-17",
            symbol="ETHUSDT",
            memory_type="buffer_summary"
        )
        
        print(f"🚀 增強版交易機器人已初始化")
        print(f"📊 交易對: ETHUSDT")
        print(f"🤖 模型: gemini-2.5-flash-lite-preview-06-17")
        print(f"💾 記憶系統: 已啟用")
        print("-" * 50)
        
        # 初始化傳統 API（用於狀態檢查）
        binance = BinanceAPI(symbol="ETHUSDT")
        
        # 獲取當前交易狀態
        print("📊 獲取交易狀態...")
        orders = binance.get_trading_status()
        main_orders = binance.get_open_futures_orders()
        
        print(f"📋 訂單數量: {len(orders)}")
        print(f"🎯 主訂單數量: {len(main_orders)}")
        
        # 執行交易分析
        print("\n🔍 執行新交易機會分析...")
        result = agent.execute_trading_analysis("new_order")
        
        print(f"\n🤖 AI 分析結果:")
        print("-" * 40)
        print(result)
        print("-" * 40)
        
        # 顯示記憶總結
        memory_summary = agent.get_memory_summary()
        if memory_summary and memory_summary != "無歷史決策記錄":
            print(f"\n💭 記憶總結:")
            print(memory_summary[:300] + "..." if len(memory_summary) > 300 else memory_summary)
        
        print("\n✅ 測試完成！")
        
    except Exception as e:
        print(f"❌ 測試失敗: {str(e)}")
        print(f"📋 錯誤詳情: {traceback.format_exc()}")


if __name__ == '__main__':
    test_trading_analysis()
