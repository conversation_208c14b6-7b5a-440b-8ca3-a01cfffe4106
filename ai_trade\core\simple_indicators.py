"""
簡化版技術指標計算
不依賴 TA-Lib，使用 pandas 和 numpy 實現
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional
from langchain_core.tools import tool

# 導入基礎 API
from .binance_api import (
    get_kline_1m, get_kline_5m, get_kline_15m, 
    get_kline_1h, get_kline_4h, get_kline_1d,
    get_default_symbol
)


def get_kline_data_simple(timeframe: str, symbol: Optional[str] = None) -> Tuple[np.ndarray, np.ndarray, np.ndarray, int]:
    """獲取 K 線數據（簡化版）"""
    if symbol is None:
        symbol = get_default_symbol()
    
    # 時間框架映射
    timeframe_map = {
        '1m': get_kline_1m,
        '5m': get_kline_5m,
        '15m': get_kline_15m,
        '1h': get_kline_1h,
        '4h': get_kline_4h,
        '1d': get_kline_1d
    }
    
    # 數據數量映射
    count_map = {
        '1m': 30,
        '5m': 120,
        '15m': 120,
        '1h': 50,
        '4h': 20,
        '1d': 7
    }
    
    get_kline_func = timeframe_map.get(timeframe, get_kline_5m)
    count = count_map.get(timeframe, 30)
    
    try:
        kline_info = get_kline_func(symbol)
        if not kline_info:
            raise ValueError(f"無法獲取 {timeframe} K線數據")
        
        # 取最近的數據
        kline_info = kline_info[-count:]
        
        # 提取價格數據
        closing_prices = np.array([float(kline[4]) for kline in kline_info])
        high_prices = np.array([float(kline[2]) for kline in kline_info])
        low_prices = np.array([float(kline[3]) for kline in kline_info])
        
        return closing_prices, high_prices, low_prices, len(kline_info)
        
    except Exception as e:
        print(f"獲取K線數據失敗: {e}")
        # 返回空數據
        return np.array([]), np.array([]), np.array([]), 0


def calculate_sma(prices: np.ndarray, period: int = 14) -> np.ndarray:
    """計算簡單移動平均線"""
    if len(prices) < period:
        return np.full(len(prices), np.nan)
    
    sma = np.full(len(prices), np.nan)
    for i in range(period - 1, len(prices)):
        sma[i] = np.mean(prices[i - period + 1:i + 1])
    
    return sma


def calculate_ema(prices: np.ndarray, period: int = 14) -> np.ndarray:
    """計算指數移動平均線"""
    if len(prices) == 0:
        return np.array([])
    
    ema = np.full(len(prices), np.nan)
    multiplier = 2 / (period + 1)
    
    # 第一個值使用 SMA
    if len(prices) >= period:
        ema[period - 1] = np.mean(prices[:period])
        
        # 計算後續的 EMA
        for i in range(period, len(prices)):
            ema[i] = (prices[i] * multiplier) + (ema[i - 1] * (1 - multiplier))
    
    return ema


def calculate_rsi(prices: np.ndarray, period: int = 14) -> np.ndarray:
    """計算相對強弱指數"""
    if len(prices) < period + 1:
        return np.full(len(prices), np.nan)
    
    # 計算價格變化
    deltas = np.diff(prices)
    gains = np.where(deltas > 0, deltas, 0)
    losses = np.where(deltas < 0, -deltas, 0)
    
    # 計算平均收益和損失
    avg_gains = np.full(len(prices), np.nan)
    avg_losses = np.full(len(prices), np.nan)
    
    # 第一個週期使用簡單平均
    if len(gains) >= period:
        avg_gains[period] = np.mean(gains[:period])
        avg_losses[period] = np.mean(losses[:period])
        
        # 後續使用指數平滑
        for i in range(period + 1, len(prices)):
            avg_gains[i] = (avg_gains[i-1] * (period - 1) + gains[i-1]) / period
            avg_losses[i] = (avg_losses[i-1] * (period - 1) + losses[i-1]) / period
    
    # 計算 RSI
    rsi = np.full(len(prices), np.nan)
    for i in range(period, len(prices)):
        if avg_losses[i] != 0:
            rs = avg_gains[i] / avg_losses[i]
            rsi[i] = 100 - (100 / (1 + rs))
        else:
            rsi[i] = 100
    
    return rsi


def calculate_macd(prices: np.ndarray, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """計算 MACD"""
    if len(prices) < slow:
        empty = np.full(len(prices), np.nan)
        return empty, empty, empty
    
    # 計算快線和慢線 EMA
    ema_fast = calculate_ema(prices, fast)
    ema_slow = calculate_ema(prices, slow)
    
    # 計算 MACD 線
    macd_line = ema_fast - ema_slow
    
    # 計算信號線
    signal_line = calculate_ema(macd_line[~np.isnan(macd_line)], signal)
    
    # 調整信號線長度
    signal_full = np.full(len(prices), np.nan)
    valid_start = slow - 1
    if len(signal_line) > 0:
        signal_full[valid_start:valid_start + len(signal_line)] = signal_line
    
    # 計算直方圖
    histogram = macd_line - signal_full
    
    return macd_line, signal_full, histogram


def calculate_atr(high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> np.ndarray:
    """計算真實波幅"""
    if len(high) < 2:
        return np.full(len(high), np.nan)
    
    # 計算真實範圍
    tr1 = high - low
    tr2 = np.abs(high - np.roll(close, 1))
    tr3 = np.abs(low - np.roll(close, 1))
    
    # 第一個值設為 NaN
    tr2[0] = np.nan
    tr3[0] = np.nan
    
    # 取最大值
    true_range = np.nanmax([tr1, tr2, tr3], axis=0)
    
    # 計算 ATR（使用 SMA）
    atr = calculate_sma(true_range, period)
    
    return atr


def calculate_kdj(high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 9) -> Tuple[np.ndarray, np.ndarray]:
    """計算 KDJ 指標"""
    if len(high) < period:
        empty = np.full(len(high), np.nan)
        return empty, empty
    
    # 計算 RSV
    rsv = np.full(len(high), np.nan)
    for i in range(period - 1, len(high)):
        highest = np.max(high[i - period + 1:i + 1])
        lowest = np.min(low[i - period + 1:i + 1])
        if highest != lowest:
            rsv[i] = (close[i] - lowest) / (highest - lowest) * 100
        else:
            rsv[i] = 50
    
    # 計算 K 和 D
    k = np.full(len(high), np.nan)
    d = np.full(len(high), np.nan)
    
    # 初始值
    k[period - 1] = rsv[period - 1]
    d[period - 1] = k[period - 1]
    
    # 計算後續值
    for i in range(period, len(high)):
        k[i] = (2/3) * k[i-1] + (1/3) * rsv[i]
        d[i] = (2/3) * d[i-1] + (1/3) * k[i]
    
    return k, d


def get_simple_technical_indicators_raw(timeframe: str, symbol: Optional[str] = None) -> Dict:
    """獲取簡化版技術指標
    
    Args:
        timeframe: 時間框架 ('1m', '5m', '15m', '1h', '4h', '1d')
        symbol: 交易對符號
    
    Returns:
        包含技術指標的字典
    """
    if symbol is None:
        symbol = get_default_symbol()
    
    try:
        # 獲取價格數據
        closing_prices, high_prices, low_prices, count = get_kline_data_simple(timeframe, symbol)
        
        if count == 0:
            return {
                'error': f'無法獲取 {timeframe} 的價格數據',
                'timeframe': timeframe,
                'symbol': symbol
            }
        
        # 計算技術指標
        indicators = {
            'timeframe': timeframe,
            'symbol': symbol,
            'data_count': count,
            'atr': calculate_atr(high_prices, low_prices, closing_prices).tolist(),
            'ma_7': calculate_sma(closing_prices, 7).tolist(),
            'ma_14': calculate_sma(closing_prices, 14).tolist(),
            'ema_14': calculate_ema(closing_prices, 14).tolist(),
            'rsi_6': calculate_rsi(closing_prices, 6).tolist(),
            'rsi_12': calculate_rsi(closing_prices, 12).tolist(),
            'rsi_24': calculate_rsi(closing_prices, 24).tolist(),
        }
        
        # 計算 MACD
        macd, macd_signal, macd_hist = calculate_macd(closing_prices)
        indicators.update({
            'macd': macd.tolist(),
            'macd_signal': macd_signal.tolist(),
            'macd_hist': macd_hist.tolist(),
        })
        
        # 計算 KDJ
        k, d = calculate_kdj(high_prices, low_prices, closing_prices)
        indicators.update({
            'kdj_k': k.tolist(),
            'kdj_d': d.tolist(),
        })
        
        return indicators
        
    except Exception as e:
        return {
            'error': f'計算技術指標失敗: {str(e)}',
            'timeframe': timeframe,
            'symbol': symbol
        }


@tool
def get_simple_technical_indicators(timeframe: str, symbol: Optional[str] = None) -> Dict:
    """獲取簡化版技術指標 (LangChain 工具版本)

    Args:
        timeframe: 時間框架 ('1m', '5m', '15m', '1h', '4h', '1d')
        symbol: 交易對符號

    Returns:
        包含技術指標的字典
    """
    return get_simple_technical_indicators_raw(timeframe, symbol)
