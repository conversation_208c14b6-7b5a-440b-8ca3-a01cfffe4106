"""
增強版交易工具
提供更好的錯誤處理、參數驗證和結果格式化
"""

from datetime import datetime
from typing import Dict, List, Optional, Union, Tuple
import json
import traceback
import numpy as np
from decimal import Decimal, ROUND_DOWN
from langchain_core.tools import tool

# 嘗試導入 pandas，如果失敗則使用替代方案
try:
    import pandas as pd
except ImportError:
    # 創建一個簡單的 pandas 替代
    class MockPandas:
        @staticmethod
        def isna(value):
            return value is None or (isinstance(value, float) and np.isnan(value))
    pd = MockPandas()

# 導入基礎工具
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.binance_api import (
    get_price as _get_price,
    get_order_book as _get_order_book,
    get_open_futures_orders as _get_position_info,
    get_futures_order_history as _get_trading_history,
    execute_buy_order as _execute_buy_order,
    execute_sell_order as _execute_sell_order,
    modify_stop_loss_take_profit as _modify_stop_loss_take_profit,
    close_position_immediately as _close_position_immediately,
    get_default_symbol
)

# 嘗試導入技術指標，優先使用 TA-Lib 版本
try:
    from core.calculate_indicators import get_technical_indicators_for_timeframe as _get_technical_indicators
except ImportError:
    from core.simple_indicators import get_simple_technical_indicators_raw as _get_technical_indicators


class TradingToolsValidator:
    """交易工具驗證器"""
    
    @staticmethod
    def validate_price(price: float, symbol: str = None) -> bool:
        """驗證價格是否合理"""
        if symbol is None:
            symbol = get_default_symbol()
        
        try:
            current_price_info = _get_price(symbol)
            current_price = float(current_price_info['price'])
            
            # 價格不能偏離當前價格超過10%
            price_diff_ratio = abs(price - current_price) / current_price
            return price_diff_ratio <= 0.1
        except:
            return False
    
    @staticmethod
    def validate_stop_loss_take_profit(entry_price: float, stop_loss: float, take_profit: float, side: str) -> Tuple[bool, str]:
        """驗證止損止盈設置"""
        if side.upper() == 'BUY':
            # 做多：止損應該低於入場價，止盈應該高於入場價
            if stop_loss >= entry_price:
                return False, "做多時止損價格應該低於入場價格"
            if take_profit <= entry_price:
                return False, "做多時止盈價格應該高於入場價格"
        elif side.upper() == 'SELL':
            # 做空：止損應該高於入場價，止盈應該低於入場價
            if stop_loss <= entry_price:
                return False, "做空時止損價格應該高於入場價格"
            if take_profit >= entry_price:
                return False, "做空時止盈價格應該低於入場價格"
        
        # 檢查風險回報比
        if side.upper() == 'BUY':
            risk = entry_price - stop_loss
            reward = take_profit - entry_price
        else:
            risk = stop_loss - entry_price
            reward = entry_price - take_profit
        
        risk_reward_ratio = reward / risk if risk > 0 else 0
        if risk_reward_ratio < 1.5:
            return False, f"風險回報比過低 ({risk_reward_ratio:.2f}:1)，建議至少1.5:1"
        
        return True, "止損止盈設置合理"


@tool
def enhanced_get_market_data(symbol: Optional[str] = None) -> Dict:
    """獲取增強版市場數據
    
    Args:
        symbol: 交易對符號，如 'ETHUSDT'
    
    Returns:
        包含市場數據和基本分析的字典
    """
    try:
        if symbol is None:
            symbol = get_default_symbol()
        
        # 獲取基礎市場數據
        price_info = _get_price(symbol)
        order_book = _get_order_book(symbol)

        # 組合市場數據
        market_data = {
            'price_info': price_info,
            'order_book': order_book
        }
        
        # 添加時間戳
        market_data['query_time'] = datetime.now().isoformat()
        
        # 添加價格變化分析
        price_info = market_data.get('price_info', {})
        if 'price' in price_info:
            current_price = float(price_info['price'])
            market_data['price_analysis'] = {
                'current_price': current_price,
                'price_level': 'unknown'
            }
        
        return market_data
        
    except Exception as e:
        return {
            'error': f"獲取市場數據失敗: {str(e)}",
            'timestamp': datetime.now().isoformat()
        }


@tool
def enhanced_get_technical_indicators(timeframe: str, symbol: Optional[str] = None) -> Dict:
    """獲取增強版技術指標
    
    Args:
        timeframe: 時間框架 ('5m', '15m', '1h', '4h')
        symbol: 交易對符號
    
    Returns:
        包含技術指標和分析的字典
    """
    try:
        if symbol is None:
            symbol = get_default_symbol()
        
        # 獲取技術指標 - 直接調用原始函數
        indicators_result = _get_technical_indicators(timeframe, symbol)

        # 處理返回結果
        if isinstance(indicators_result, dict):
            if 'error' in indicators_result:
                # 如果有錯誤，直接返回錯誤信息
                return indicators_result
            indicators = indicators_result
        else:
            indicators = indicators_result if indicators_result else {}
        
        # 添加指標分析
        analysis = analyze_indicators(indicators)
        
        return {
            'timeframe': timeframe,
            'symbol': symbol,
            'indicators': indicators,
            'analysis': analysis,
            'timestamp': datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            'error': f"獲取技術指標失敗: {str(e)}",
            'timeframe': timeframe,
            'symbol': symbol,
            'timestamp': datetime.now().isoformat()
        }


def analyze_indicators(indicators: Dict) -> Dict:
    """分析技術指標"""
    analysis = {
        'trend': 'neutral',
        'momentum': 'neutral',
        'volatility': 'normal',
        'signals': []
    }
    
    try:
        # RSI 分析
        rsi_12 = indicators.get('rsi_12', [])
        if rsi_12 and len(rsi_12) > 0:
            current_rsi = rsi_12[-1]
            if not pd.isna(current_rsi):
                if current_rsi > 70:
                    analysis['momentum'] = 'overbought'
                    analysis['signals'].append('RSI超買')
                elif current_rsi < 30:
                    analysis['momentum'] = 'oversold'
                    analysis['signals'].append('RSI超賣')
        
        # MACD 分析
        macd_hist = indicators.get('macd_hist', [])
        if macd_hist and len(macd_hist) > 1:
            current_hist = macd_hist[-1]
            prev_hist = macd_hist[-2]
            if not pd.isna(current_hist) and not pd.isna(prev_hist):
                if current_hist > 0 and prev_hist <= 0:
                    analysis['signals'].append('MACD金叉')
                elif current_hist < 0 and prev_hist >= 0:
                    analysis['signals'].append('MACD死叉')
        
        # 移動平均線分析
        ma_7 = indicators.get('ma_7', [])
        ma_14 = indicators.get('ma_14', [])
        if ma_7 and ma_14 and len(ma_7) > 0 and len(ma_14) > 0:
            current_ma7 = ma_7[-1]
            current_ma14 = ma_14[-1]
            if not pd.isna(current_ma7) and not pd.isna(current_ma14):
                if current_ma7 > current_ma14:
                    analysis['trend'] = 'bullish'
                elif current_ma7 < current_ma14:
                    analysis['trend'] = 'bearish'
        
    except Exception as e:
        analysis['error'] = f"指標分析失敗: {str(e)}"
    
    return analysis


@tool
def enhanced_execute_buy_order(
    price: float,
    stop_loss_price: float,
    take_profit_price: float,
    symbol: Optional[str] = None,
    quantity_usd: float = 20.0,
    reasoning: str = ""
) -> Dict:
    """執行增強版買入訂單（做多）
    
    Args:
        price: 買入價格
        stop_loss_price: 止損價格
        take_profit_price: 止盈價格
        symbol: 交易對符號
        quantity_usd: 交易金額（美元）
        reasoning: 交易推理
    
    Returns:
        訂單執行結果
    """
    try:
        if symbol is None:
            symbol = get_default_symbol()
        
        # 驗證參數
        validator = TradingToolsValidator()
        
        # 驗證價格
        if not validator.validate_price(price, symbol):
            return {
                'success': False,
                'error': '入場價格偏離當前市價過多（超過10%）',
                'timestamp': datetime.now().isoformat()
            }
        
        # 驗證止損止盈
        is_valid, message = validator.validate_stop_loss_take_profit(
            price, stop_loss_price, take_profit_price, 'BUY'
        )
        if not is_valid:
            return {
                'success': False,
                'error': message,
                'timestamp': datetime.now().isoformat()
            }
        
        # 執行訂單
        result = _execute_buy_order(price, stop_loss_price, take_profit_price, symbol, quantity_usd)
        
        # 添加額外信息
        result['reasoning'] = reasoning
        result['risk_reward_ratio'] = (take_profit_price - price) / (price - stop_loss_price)
        result['timestamp'] = datetime.now().isoformat()
        
        return result
        
    except Exception as e:
        return {
            'success': False,
            'error': f"執行買入訂單失敗: {str(e)}",
            'traceback': traceback.format_exc(),
            'timestamp': datetime.now().isoformat()
        }


@tool
def enhanced_execute_sell_order(
    price: float,
    stop_loss_price: float,
    take_profit_price: float,
    symbol: Optional[str] = None,
    quantity_usd: float = 20.0,
    reasoning: str = ""
) -> Dict:
    """執行增強版賣出訂單（做空）
    
    Args:
        price: 賣出價格
        stop_loss_price: 止損價格
        take_profit_price: 止盈價格
        symbol: 交易對符號
        quantity_usd: 交易金額（美元）
        reasoning: 交易推理
    
    Returns:
        訂單執行結果
    """
    try:
        if symbol is None:
            symbol = get_default_symbol()
        
        # 驗證參數
        validator = TradingToolsValidator()
        
        # 驗證價格
        if not validator.validate_price(price, symbol):
            return {
                'success': False,
                'error': '入場價格偏離當前市價過多（超過10%）',
                'timestamp': datetime.now().isoformat()
            }
        
        # 驗證止損止盈
        is_valid, message = validator.validate_stop_loss_take_profit(
            price, stop_loss_price, take_profit_price, 'SELL'
        )
        if not is_valid:
            return {
                'success': False,
                'error': message,
                'timestamp': datetime.now().isoformat()
            }
        
        # 執行訂單
        result = _execute_sell_order(price, stop_loss_price, take_profit_price, symbol, quantity_usd)
        
        # 添加額外信息
        result['reasoning'] = reasoning
        result['risk_reward_ratio'] = (price - take_profit_price) / (stop_loss_price - price)
        result['timestamp'] = datetime.now().isoformat()
        
        return result
        
    except Exception as e:
        return {
            'success': False,
            'error': f"執行賣出訂單失敗: {str(e)}",
            'traceback': traceback.format_exc(),
            'timestamp': datetime.now().isoformat()
        }
