# 🚀 增強版 LangChain 交易機器人

基於 LangChain 的智能加密貨幣交易機器人，具備完整的記憶管理、決策追蹤和風險控制功能。

## ✨ 核心特性

- **🧠 智能決策**: LangChain 代理架構，增強記憶管理系統
- **📊 技術分析**: 多時間框架分析，完整技術指標支持
- **🛡️ 風險管理**: 智能止損止盈，動態倉位管理
- **🔄 自動執行**: 靈活調度系統，錯誤恢復機制

## 🚀 快速開始

### 環境設置
```bash
# 激活虛擬環境
.venv\Scripts\activate  # Windows

# 確保依賴已安裝
pip install -r requirements.txt
```

### 配置文件
確保 `.env` 文件包含必要的 API 密鑰：
```env
GOOGLE_API_KEY=your_google_api_key
binance_api=your_binance_api_key
binance_key=your_binance_secret_key
```

### 運行機器人
```bash
python enhanced_main.py
```

## 💡 使用示例

### 程式化調用
```python
from agents.advanced_langchain_agent import AdvancedTradingAgent

# 創建代理
agent = AdvancedTradingAgent(symbol='ETHUSDT')

# 執行分析
result = agent.execute_trading_analysis("new_order")
print(result)
```

## 🧠 核心功能

- **記憶管理**: 持久化交易決策歷史，從成功和失敗中學習
- **智能分析**: 多時間框架技術分析，風險回報比計算
- **自動執行**: 靈活調度系統，支持 APScheduler 或簡單循環
- **錯誤恢復**: 智能錯誤處理和自動恢復機制

## ⚠️ 重要提醒

1. **風險管理**: 請在測試環境中充分測試後再用於實盤交易
2. **API 安全**: 妥善保管 API 密鑰，設置適當的權限
3. **資金管理**: 建議單筆交易風險不超過總資金的2%
4. **監控**: 定期檢查系統運行狀態和交易結果

## 📞 支持

如遇問題請檢查：
1. 環境變量配置是否正確
2. 虛擬環境是否已激活
3. 依賴包是否完整安裝
