"""
測試趨勢分析工具修復
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import traceback
from agents.advanced_langchain_agent import AdvancedTradingAgent

def test_trend_analysis():
    """測試趨勢分析工具"""
    try:
        print("🔍 測試趨勢分析工具修復...")
        
        # 創建代理
        agent = AdvancedTradingAgent(
            symbol="ETHUSDT",
            model_name="gemini-2.5-flash-lite-preview-06-17"
        )
        
        print("✅ 代理創建成功")
        
        # 獲取趨勢分析工具
        tools = agent.tools
        trend_tool = None
        
        for tool in tools:
            if hasattr(tool, 'name') and 'analyze_market_trend' in tool.name:
                trend_tool = tool
                break
        
        if trend_tool:
            print("✅ 找到趨勢分析工具")
            
            # 測試工具調用
            print("🔍 測試工具調用...")
            result = trend_tool.invoke({"timeframe_list": "5m,15m,1h"})
            
            print("✅ 工具調用成功！")
            print("📊 結果:")
            print(result)
            
        else:
            print("❌ 未找到趨勢分析工具")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {str(e)}")
        print(f"📋 錯誤詳情: {traceback.format_exc()}")
        return False

def test_full_analysis():
    """測試完整分析流程"""
    try:
        print("\n🔍 測試完整分析流程...")
        
        # 創建代理
        agent = AdvancedTradingAgent(
            symbol="ETHUSDT",
            model_name="gemini-2.5-flash-lite-preview-06-17"
        )
        
        # 執行新訂單分析
        print("🚀 執行新訂單分析...")
        result = agent.execute_trading_analysis("new_order")
        
        print("✅ 分析完成！")
        print("📊 結果預覽:")
        print(result[:500] + "..." if len(result) > 500 else result)
        
        return True
        
    except Exception as e:
        print(f"❌ 完整分析測試失敗: {str(e)}")
        print(f"📋 錯誤詳情: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    try:
        # 加載環境變量
        import dotenv
        dotenv.load_dotenv()
        
        print("🚀 開始趨勢分析工具測試")
        print("=" * 50)
        
        # 測試工具調用
        success1 = test_trend_analysis()
        
        # 測試完整分析
        success2 = test_full_analysis()
        
        print("\n" + "=" * 50)
        if success1 and success2:
            print("🎉 所有測試通過！趨勢分析工具已修復")
        else:
            print("⚠️ 部分測試失敗，請檢查相關問題")
            
    except Exception as e:
        print(f"❌ 測試腳本執行失敗: {str(e)}")
        print(f"📋 錯誤詳情: {traceback.format_exc()}")
