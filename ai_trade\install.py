"""
自動安裝腳本
智能檢測並安裝所需依賴
"""

import subprocess
import sys
import os
import platform
from pathlib import Path


def run_command(command, description=""):
    """執行命令並處理錯誤"""
    print(f"🔄 {description}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} 成功")
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失敗: {e.stderr}")
        return False, e.stderr


def check_python_version():
    """檢查 Python 版本"""
    version = sys.version_info
    print(f"🐍 Python 版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要 Python 3.8 或更高版本")
        return False
    
    print("✅ Python 版本符合要求")
    return True


def install_basic_requirements():
    """安裝基礎依賴"""
    print("\n📦 安裝基礎依賴...")
    
    # 升級 pip
    success, _ = run_command(
        f"{sys.executable} -m pip install --upgrade pip",
        "升級 pip"
    )
    
    if not success:
        print("⚠️ pip 升級失敗，繼續安裝...")
    
    # 安裝基礎依賴
    success, _ = run_command(
        f"{sys.executable} -m pip install -r requirements_simple.txt",
        "安裝基礎依賴"
    )
    
    return success


def install_talib():
    """嘗試安裝 TA-Lib"""
    print("\n🔧 嘗試安裝 TA-Lib...")
    
    system = platform.system().lower()
    
    if system == "windows":
        return install_talib_windows()
    elif system == "darwin":  # macOS
        return install_talib_macos()
    elif system == "linux":
        return install_talib_linux()
    else:
        print(f"⚠️ 不支持的操作系統: {system}")
        return False


def install_talib_windows():
    """Windows 系統安裝 TA-Lib"""
    print("🪟 檢測到 Windows 系統")
    
    # 方法1: 嘗試預編譯的 wheel
    python_version = f"{sys.version_info.major}{sys.version_info.minor}"
    architecture = "win_amd64" if platform.machine().endswith('64') else "win32"
    
    wheel_url = f"https://github.com/cgohlke/talib-build/releases/download/v0.4.28/TA_Lib-0.4.28-cp{python_version}-cp{python_version}-{architecture}.whl"
    
    success, _ = run_command(
        f"{sys.executable} -m pip install {wheel_url}",
        f"安裝預編譯的 TA-Lib wheel (Python {python_version}, {architecture})"
    )
    
    if success:
        return True
    
    # 方法2: 嘗試直接安裝
    print("🔄 嘗試直接安裝 TA-Lib...")
    success, _ = run_command(
        f"{sys.executable} -m pip install TA-Lib",
        "直接安裝 TA-Lib"
    )
    
    return success


def install_talib_macos():
    """macOS 系統安裝 TA-Lib"""
    print("🍎 檢測到 macOS 系統")
    
    # 檢查是否有 Homebrew
    success, _ = run_command("brew --version", "檢查 Homebrew")
    
    if success:
        # 使用 Homebrew 安裝 C 庫
        run_command("brew install ta-lib", "安裝 TA-Lib C 庫")
    else:
        print("⚠️ 未檢測到 Homebrew，請手動安裝 TA-Lib C 庫")
    
    # 安裝 Python 包
    success, _ = run_command(
        f"{sys.executable} -m pip install TA-Lib",
        "安裝 TA-Lib Python 包"
    )
    
    return success


def install_talib_linux():
    """Linux 系統安裝 TA-Lib"""
    print("🐧 檢測到 Linux 系統")
    
    # 嘗試安裝系統依賴
    distro_commands = [
        ("sudo apt-get update && sudo apt-get install -y libta-lib-dev", "Ubuntu/Debian"),
        ("sudo yum install -y ta-lib-devel", "CentOS/RHEL"),
        ("sudo dnf install -y ta-lib-devel", "Fedora")
    ]
    
    for cmd, distro in distro_commands:
        print(f"🔄 嘗試 {distro} 安裝方式...")
        success, _ = run_command(cmd, f"安裝 TA-Lib 系統依賴 ({distro})")
        if success:
            break
    
    # 安裝 Python 包
    success, _ = run_command(
        f"{sys.executable} -m pip install TA-Lib",
        "安裝 TA-Lib Python 包"
    )
    
    return success


def test_installation():
    """測試安裝結果"""
    print("\n🧪 測試安裝結果...")
    
    # 測試基礎依賴
    basic_packages = [
        "langchain",
        "langchain_core", 
        "langchain_google_genai",
        "binance",
        "numpy",
        "pandas",
        "apscheduler",
        "dotenv"
    ]
    
    failed_packages = []
    
    for package in basic_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            failed_packages.append(package)
    
    # 測試 TA-Lib
    try:
        import talib
        # 嘗試調用一個簡單的函數來確保真正可用
        import numpy as np
        test_data = np.array([1.0, 2.0, 3.0, 4.0, 5.0])
        talib.SMA(test_data, timeperiod=3)
        print("✅ talib (TA-Lib)")
        talib_available = True
    except (ImportError, ValueError, Exception) as e:
        print(f"⚠️ talib (TA-Lib) - 將使用簡化版技術指標 ({str(e)[:50]}...)")
        talib_available = False
    
    return len(failed_packages) == 0, talib_available


def main():
    """主安裝流程"""
    print("🚀 AI Trade 自動安裝腳本")
    print("=" * 50)
    
    # 檢查 Python 版本
    if not check_python_version():
        sys.exit(1)
    
    # 檢查文件
    if not Path("requirements_simple.txt").exists():
        print("❌ 找不到 requirements_simple.txt 文件")
        sys.exit(1)
    
    # 安裝基礎依賴
    if not install_basic_requirements():
        print("❌ 基礎依賴安裝失敗")
        sys.exit(1)
    
    # 嘗試安裝 TA-Lib
    talib_success = install_talib()
    
    # 測試安裝
    basic_success, talib_available = test_installation()
    
    print("\n" + "=" * 50)
    print("📊 安裝結果總結:")
    
    if basic_success:
        print("✅ 基礎依賴安裝成功")
    else:
        print("❌ 部分基礎依賴安裝失敗")
    
    if talib_available:
        print("✅ TA-Lib 安裝成功，將使用完整版技術指標")
    else:
        print("⚠️ TA-Lib 未安裝，將使用簡化版技術指標")
    
    if basic_success:
        print("\n🎉 安裝完成！您可以開始使用 AI Trade 了")
        print("💡 運行以下命令開始:")
        print("   python main.py --check  # 檢查環境配置")
        print("   python main.py          # 啟動交易機器人")
    else:
        print("\n❌ 安裝未完全成功，請檢查錯誤信息")
        print("💡 您可以:")
        print("   1. 手動安裝失敗的依賴")
        print("   2. 查看 INSTALL_GUIDE.md 獲取詳細指導")
        print("   3. 使用虛擬環境重新安裝")


if __name__ == "__main__":
    main()
