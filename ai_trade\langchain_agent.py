from langchain.prompts import PromptTemplate
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.agents import create_tool_calling_agent, AgentExecutor
from langchain_core.tools import tool
from langchain_core.prompts import ChatPromptTemplate
from binance_api import (
    BinanceAPI, get_price, get_kline_5m, get_kline_15m, get_kline_1h, get_kline_4h,
    get_open_futures_orders, get_futures_order_history, set_default_symbol,
    execute_buy_order, execute_sell_order, modify_stop_loss_take_profit,
    close_position_immediately
)
from calculate_indicators import TechnicalIndicators, get_kline_and_calculate_indicators
from datetime import datetime, timedelta
import json
from typing import Dict, List, Optional

# 定義一些工具函數供 LangChain Agent 使用
@tool
def get_market_data(symbol: str = 'ETHUSDT') -> Dict:
    """獲取市場數據包括價格和K線資訊

    Args:
        symbol: 交易對符號

    Returns:
        包含價格和K線資訊的字典
    """
    price_info = get_price(symbol)
    kline_5m = get_kline_5m(symbol)
    kline_15m = get_kline_15m(symbol)
    kline_1h = get_kline_1h(symbol)
    kline_4h = get_kline_4h(symbol)

    return {
        'price_info': price_info,
        'kline_5m': kline_5m[-10:],  # 最近10根K線
        'kline_15m': kline_15m[-10:],
        'kline_1h': kline_1h[-10:],
        'kline_4h': kline_4h[-10:]
    }

@tool
def get_technical_indicators_for_timeframe(timeframe: str, symbol: str = 'ETHUSDT') -> Dict:
    """獲取指定時間框架的技術指標

    Args:
        timeframe: 時間框架
        symbol: 交易對符號

    Returns:
        技術指標字典
    """
    return get_kline_and_calculate_indicators(timeframe, symbol)

@tool
def get_position_info(symbol: str = 'ETHUSDT') -> List:
    """獲取當前持倉資訊

    Args:
        symbol: 交易對符號

    Returns:
        持倉資訊列表
    """
    return get_open_futures_orders(symbol)

@tool
def get_trading_history(symbol: str = 'ETHUSDT') -> List:
    """獲取交易歷史

    Args:
        symbol: 交易對符號

    Returns:
        交易歷史列表
    """
    return get_futures_order_history(symbol)

class LangchainAgent:
    def __init__(self, model_name="gemini-1.5-flash-latest", symbol='ETHUSDT'):
        self.llm = ChatGoogleGenerativeAI(model=model_name, temperature=0)
        self.symbol = symbol
        set_default_symbol(symbol)  # 設置默認交易對
        self.binance = BinanceAPI(symbol=symbol)  # 保持向後兼容
        self.file_path = 'order_record.json'
        self.reason_file = 'reason.txt'
        self.modify_reason_file = 'modify_reason.txt'
        self.no_order_reason_file = 'no_order_reason.txt'

        # 定義可用的工具 - 包括交易執行工具
        self.tools = [
            get_market_data,
            get_technical_indicators_for_timeframe,
            get_position_info,
            get_trading_history,
            execute_buy_order,
            execute_sell_order,
            modify_stop_loss_take_profit,
            close_position_immediately
        ]

        # 創建 LangChain Agent
        self.agent = self._create_agent()
        self.agent_executor = AgentExecutor(agent=self.agent, tools=self.tools, verbose=True)

    def _create_agent(self):
        """創建 LangChain Agent"""
        prompt = ChatPromptTemplate.from_messages([
            ("system", self.get_system_prompt()),
            ("human", "{input}"),
            ("placeholder", "{agent_scratchpad}")
        ])

        return create_tool_calling_agent(self.llm, self.tools, prompt)

    def get_available_tools(self):
        """獲取可用的工具列表"""
        return self.tools

    def execute_trading_decision(self, market_context: str) -> str:
        """執行交易決策 - 讓 LLM 直接調用工具"""
        try:
            result = self.agent_executor.invoke({
                "input": market_context
            })
            return result["output"]
        except Exception as e:
            return f"執行交易決策時發生錯誤：{str(e)}"

    def get_system_prompt(self) -> str:
        """獲取系統提示詞"""
        return """
你是一個專業的短線交易員，專注於捕捉短週期K線（如5分鐘、15分鐘）的信號，同時使用1小時和4小時K線作為輔助，用於確定持倉週期和風險管理。你的目標是在短時間週期內快速捕捉盈利機會，並在短期市場波動中及時退出，避免頻繁交易帶來的過度手續費和擴大的風險。

**重要說明：**
1. 你有權直接執行交易操作，包括開倉、平倉、修改止損止盈等
2. 使用20倍槓桿進行交易，請在最優價格進入市場，避免高槓桿帶來的過度手續費和風險放大
3. 入場點主要基於短週期K線的形態和成交量變化，輔以RSI、MACD、ATR、吞沒形態、十字星等技術指標的綜合判斷
4. 長週期K線（1小時、4小時）僅用於確認大趨勢和整體市場方向，但不限制短線交易的入場決策
5. 不支持加倉或部分止盈止損，確保開倉時的風險是可控的

**可用工具：**
- get_market_data: 獲取市場數據
- get_technical_indicators_for_timeframe: 獲取技術指標
- get_position_info: 獲取當前持倉信息
- get_trading_history: 獲取交易歷史
- execute_buy_order: 執行買入訂單（做多）
- execute_sell_order: 執行賣出訂單（做空）
- modify_stop_loss_take_profit: 修改止損止盈
- close_position_immediately: 立即平倉

**交易策略：**
- 在支撐位考慮做多，在阻力位考慮做空
- 如果空頭趨勢占主導，技術指標或K線形態出現短期反彈跡象時，**耐心等待反彈結束後再做空**
- 如果多頭趨勢占主導，但出現短期回調跡象時，**耐心等待回調完成後再做多**
- 當市場出現強勢動能信號時（如RSI快速突破超買超賣區域或MACD形成明確交叉），可採取相應行動

請根據當前市場情況分析並決定是否需要執行交易操作。如果需要交易，請直接調用相應的工具執行。
"""

    def get_market_context_for_new_order(self) -> str:
        """獲取新訂單的市場背景信息"""
        try:
            # 讀取歷史文件
            with open(self.reason_file, 'r', encoding='utf-8') as f:
                reason = f.read()
            with open(self.modify_reason_file, 'r', encoding='utf-8') as f:
                modify_reason = f.read()
            with open(self.no_order_reason_file, 'r', encoding='utf-8') as f:
                no_order_reason = f.read()

            # 獲取市場數據
            market_data = get_market_data(self.symbol)
            order_history = self._calculate_order_pnl()

            context = f"""
當前市場分析請求：

**當前市場信息：**
- 當前時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 當前價格: {market_data['price_info']['symbol']} = {float(market_data['price_info']['price']):.1f}

**技術分析：**
請使用 get_technical_indicators_for_timeframe 工具獲取各時間框架的技術指標進行分析。

**歷史背景：**
"""

            if reason:
                context += f"上次交易原因：{reason}\n"
            if modify_reason:
                context += f"上次修改原因：{modify_reason}\n"
            if no_order_reason:
                context += f"上次觀望原因：{no_order_reason}\n"

            if order_history:
                context += "\n**最近交易記錄：**\n"
                for order in order_history[-3:]:
                    context += f"- {order['update_time']} {'做多' if order['side'] == 'BUY' else '做空'} 盈虧: {order['net_pnl_percentage']}%\n"

            context += """

請分析當前市場情況，如果發現交易機會，請直接使用相應的工具執行交易。如果不適合交易，請說明原因。
"""
            return context

        except Exception as e:
            return f"獲取市場背景信息時發生錯誤：{str(e)}"

    def get_market_context_for_position_management(self, order_record) -> str:
        """獲取持倉管理的市場背景信息"""
        try:
            # 讀取歷史文件
            with open(self.reason_file, 'r', encoding='utf-8') as f:
                reason = f.read()
            with open(self.modify_reason_file, 'r', encoding='utf-8') as f:
                modify_reason = f.read()

            # 獲取當前持倉信息
            positions = get_position_info(self.symbol)
            if not positions:
                return "沒有找到當前持倉信息"

            position = positions[0]
            mark_price = float(position['markPrice'])
            break_even_price = float(position['breakEvenPrice'])
            unrealized_profit = float(position['unRealizedProfit'])
            notional = abs(float(position['notional']))
            pnl_rate = unrealized_profit * 100 / (notional / 20)

            context = f"""
持倉管理分析請求：

**當前持倉信息：**
- 當前時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 標記價格: {mark_price:.2f}
- 盈虧比例: {pnl_rate:.2f}%
- 未實現盈虧: {unrealized_profit:.2f}

**開倉邏輯：**
{reason if reason else '無記錄'}

**上次修改原因：**
{modify_reason if modify_reason else '無記錄'}

**技術分析：**
請使用 get_technical_indicators_for_timeframe 工具獲取各時間框架的技術指標進行分析。

請根據當前市場情況和持倉狀態，決定是否需要：
1. 修改止損止盈價格（使用 modify_stop_loss_take_profit 工具）
2. 立即平倉（使用 close_position_immediately 工具）
3. 保持當前設置

請直接使用相應的工具執行操作。
"""
            return context

        except Exception as e:
            return f"獲取持倉管理背景信息時發生錯誤：{str(e)}"

    def analyze_and_execute_new_order(self) -> str:
        """分析市場並執行新訂單決策"""
        market_context = self.get_market_context_for_new_order()
        return self.execute_trading_decision(market_context)

    def analyze_and_manage_position(self, order_record) -> str:
        """分析市場並管理現有持倉"""
        market_context = self.get_market_context_for_position_management(order_record)
        return self.execute_trading_decision(market_context)

    def format_kline_data(self, kline_data, time_frame):
        """Formats K-line data and adds technical indicator information."""
        # 根據時間框架確定數據數量
        count_map = {
            '1分鐘': 30,
            '5分鐘': 120,
            '15分鐘': 120,
            '1小時': 50,
            '4小時': 20,
            '1天': 7
        }

        count = count_map.get(time_frame, 30)
        kline_data = kline_data[-count:]

        # 使用新的工具函數獲取技術指標
        indicators = get_kline_and_calculate_indicators(time_frame, self.symbol)
        formatted_data = f"Recent K-line information (each K-line represents {time_frame}):\n"
        for i in range(len(kline_data)-1):
            kline = kline_data[i]
            timestamp_s = kline[0] / 1000
            formatted_data += (
                f'{{"Open time": "{datetime.fromtimestamp(timestamp_s).strftime("%Y-%m-%d %H:%M:%S")}", '
                f'"Open price": {"{:.2f}".format(float(kline[1]))}, '
                f'"High price": {"{:.2f}".format(float(kline[2]))}, '
                f'"Low price": {"{:.2f}".format(float(kline[3]))}, '
                f'"Close price": {"{:.2f}".format(float(kline[4]))}, '
                f'"Volume": {"{:.2f}".format(float(kline[5]))}, '
                f'"Technical Indicator Information": {{'
                f'"Average True Range (ATR)": {indicators["atr"][i]:.2f},'
                f'"Moving Average (7)": {indicators["moving_average_7"][i]:.2f},'
                f'"Moving Average (14)": {indicators["moving_average_14"][i]:.2f},'
                f'"Exponential Moving Average (EMA)": {indicators["ema"][i]:.2f},'
                f'"Relative Strength Index (RSI)(6)": {indicators["rsi_6"][i]:.2f},'
                f'"Relative Strength Index (RSI)(12)": {indicators["rsi_12"][i]:.2f},'
                f'"Relative Strength Index (RSI)(24)": {indicators["rsi_24"][i]:.2f},'
                f'"MACD": {indicators["macd"][i]:.2f},'
                f'"MACD Signal": {indicators["macd_signal"][i]:.2f},'
                f'"MACD Histogram": {indicators["macd_hist"][i]:.2f},'
                f'"KDJ K Value": {indicators["kdj_k"][i]:.2f},'
                f'"KDJ D Value": {indicators["kdj_d"][i]:.2f}'
                f'}}}}\n'
            )

        formatted_data += (
            f'\nCurrent {time_frame} K-line information (not yet closed):\n'
            f'{{"Open time": "{datetime.fromtimestamp(kline_data[-1][0] / 1000).strftime("%Y-%m-%d %H:%M:%S")}", '
            f'"Open price": {"{:.2f}".format(float(kline_data[-1][1]))}, '
            f'"High price": {"{:.2f}".format(float(kline_data[-1][2]))}, '
            f'"Low price": {"{:.2f}".format(float(kline_data[-1][3]))}, '
            f'"Latest price": {"{:.2f}".format(float(kline_data[-1][4]))}, '
            f'"Volume": {"{:.2f}".format(float(kline_data[-1][5]))}}}\n\n'
            )

        return formatted_data

    def _calculate_order_pnl(self):
        # 使用新的工具函數獲取歷史訂單
        history_orders = get_futures_order_history(self.symbol)
        if len(history_orders) == 0:
            return []
        if history_orders[-1]['realizedPnl'] == '0':
            history_orders.pop()
        open_orders = []
        stop_orders = []
        error_correction = 0
        for i in range(len(history_orders)-1,0,-2):
            i = i + error_correction
            if history_orders[i]['buyer'] != history_orders[i-1]['buyer'] and float(history_orders[i]['qty']) == float(history_orders[i-1]['qty']):
                open_orders.append(history_orders[i-1])
                stop_orders.append(history_orders[i])
            elif float(history_orders[i]['qty']) > float(history_orders[i-1]['qty']):
                buy_order = {
                    "time": history_orders[i-1]['time'],
                    "price": history_orders[i-1]['price'],
                    "quoteQty": float(history_orders[i-1]['quoteQty']) + float(history_orders[i-2]['quoteQty']),
                    "side": history_orders[i]['side'],
                    "realizedPnl": float(history_orders[i]['realizedPnl']) + float(history_orders[i-2]['realizedPnl']),
                    "commission": float(history_orders[i]['commission']) + float(history_orders[i-2]['commission'])
                }
                open_orders.append(buy_order)
                stop_orders.append(history_orders[i])
                history_orders.remove(history_orders[i-2])
                error_correction -= 1
            elif float(history_orders[i]['qty']) < float(history_orders[i-1]['qty']):
                stop_order = {
                    "time": history_orders[i]['time'],
                    "price": history_orders[i]['price'],
                    "quoteQty": float(history_orders[i]['quoteQty']) + float(history_orders[i-2]['quoteQty']),
                    "side": history_orders[i]['side'],
                    "realizedPnl": float(history_orders[i]['realizedPnl']) + float(history_orders[i-1]['realizedPnl']),
                    "commission": float(history_orders[i]['commission']) + float(history_orders[i-1]['commission'])
                }
                open_orders.append(history_orders[i-2])
                stop_orders.append(stop_order)
                history_orders.remove(history_orders[i-1])
                error_correction -= 1

        results = []
        for i in range(min(len(open_orders), len(stop_orders)),0,-1):
            open_order = open_orders[i-1]
            stop_order = stop_orders[i-1]
            stop_price = float(stop_order['price'])
            entry_price = float(open_order['price'])
            pnl = float(stop_order['realizedPnl'])
            open_order_commission = float(open_order['commission'])
            stop_order_commission = float(stop_order['commission'])
            results.append({
                "update_time": datetime.fromtimestamp(open_order['time'] / 1000).strftime('%Y-%m-%d %H:%M:%S'),
                "stop_time": datetime.fromtimestamp(stop_order['time'] / 1000).strftime('%Y-%m-%d %H:%M:%S'),
                "side": open_order['side'],
                "open_price": round(entry_price,2),
                "stop_price": round(stop_price,2),
                "pnl_percentage": f"{(pnl/(float(open_order['quoteQty']) / 20))*100:.2f}",
                "net_pnl_percentage": f"{((pnl-open_order_commission-stop_order_commission)/(float(open_order['quoteQty']) / 20))*100:.2f}"
            })

        return results

    def get_prompt_for_new_order(self):
        with open(self.reason_file, 'r', encoding='utf-8') as f:
            reason = f.read()
        with open(self.modify_reason_file, 'r', encoding='utf-8') as f:
            modify_reason = f.read()
        with open(self.no_order_reason_file, 'r', encoding='utf-8') as f:
            no_order_reason = f.read()

        # 使用新的工具函數獲取數據
        kline_info_5m = get_kline_5m(self.symbol)
        kline_info_15m = get_kline_15m(self.symbol)
        kline_info_1h = get_kline_1h(self.symbol)
        kline_info_4h = get_kline_4h(self.symbol)
        price_info = get_price(self.symbol)
        order_history = self._calculate_order_pnl()

        order_history_section = ""
        if len(order_history) != 0:
            profit = "win" if float(order_history[-1]['net_pnl_percentage']) > 0 else "loss"
            order_history_section += "Here is your recent order history:\n"
            for order in order_history[-5:]:
                order_history_section += (
                    f"  - Order Time: {order['update_time']}\n"
                    f"  - Close Time: {order['stop_time']}\n"
                    f"  - Order Side: {'Long' if order['side'] == 'BUY' else 'Short'}\n"
                    f"  - Open Price: {order['open_price']}\n"
                    f"  - Close Price: {order['stop_price']}\n"
                    f"  - PNL Percentage: {order['pnl_percentage']}%\n"
                    f"  - Net PNL Percentage: {order['net_pnl_percentage']}%\n\n"
                )
            if reason:
                order_history_section += f"Reason for the last order:\n- {reason}\n"
            if modify_reason:
                order_history_section += f"Reason for the last order {'take profit' if profit == 'win' else 'stop loss'}:\n- {modify_reason}\n\n"
            
            time_obj = datetime.strptime(order_history[-1]['update_time'] , "%Y-%m-%d %H:%M:%S")
            now = datetime.now()
            time_diff = now - time_obj
            if abs(time_diff) < timedelta(minutes=15):
                if profit == 'win':
                    order_history_section += (
                            "Hint: The last order was a take profit. Analyze the reason for the last take profit in conjunction with the current market information. If the trend is slowing down or there is a risk of a short-term rebound, please wait and see to **avoid turning profits into losses and paying high fees due to overtrading**.\n\n"
                            )
                else:
                    order_history_section += (
                            "Hint: The last order was a stop loss. Please determine if the reason for the stop loss has been reversed. If there is no obvious reversal, do not trade again to avoid losses and high fees.\n\n"
                            )
            else:
                order_history_section += (
                    "Hint: Please judge whether there is a new trading opportunity based on the profit and loss of the last order and propose new trading suggestions.\n\n"
                )

        no_order_reason_section = ""
        if no_order_reason:
            no_order_reason_section += f"Reason for choosing to observe last time:\n- {no_order_reason}\n\n"
            no_order_reason_section += ("Hint: Based on the reason for choosing to observe last time, determine:\n"
                       "1. Is there any indicator that overturns the reason for observing last time?\n"
                       "2. Is there a new trading opportunity?\n"
                       "3. Is there a new support/resistance level to confirm the trend change?\n"
                       "4. If you decide to continue to observe, what are the reasons why it is not suitable to go long or short at present?\n\n")

        kline_data = ""
        kline_data += self.format_kline_data(kline_info_5m, "5分鐘")
        kline_data += self.format_kline_data(kline_info_15m, "15分鐘")
        kline_data += self.format_kline_data(kline_info_1h, "1小時")
        kline_data += self.format_kline_data(kline_info_4h, "4小時")

        additional_conditions = ""
        if len(order_history) > 0:
            profit = "win" if float(order_history[-1]['net_pnl_percentage']) > 0 else "loss"
            if profit == 'loss':
                additional_conditions += (
                    "### Additional Conditions for Opening a Position After a Stop Loss:\n"
                    f"Since the last order resulted in a stop loss, the following **all conditions** must be met to allow a {'long' if order_history[-1]['side'] == 'BUY' else 'short'} position:\n"
                    "1. The price has broken through the ±1% range of the last order's opening price in the opposite direction.\n"
                    "2. The 5-minute K-line shows at least two K-lines confirming a trend reversal (such as an engulfing pattern).\n"
                    f"3. The RSI(24) has deviated by more than 5 points from the time of the last order (e.g., {'RSI increased from 40 to 45 for a long position' if order_history[-1]['side'] == 'BUY' else 'RSI decreased from 50 to 45 for a short position'}).\n"
                    "4. The MACD histogram has expanded its reverse momentum for 3 consecutive K-lines.\n\n"
                    "Even if the conditions are met, you should still judge whether it is the best time to enter the market based on the entry strategy and recent K-line information and technical indicators!\n\n"
                )
            elif profit == 'win':
                additional_conditions += (
                    "### Additional Conditions for Opening a Position After a Take Profit:\n"
                    f"Since the last order resulted in a take profit, the following **all conditions** must be met to allow a {'long' if order_history[-1]['side'] == 'BUY' else 'short'} position:\n"
                    "1. The price has broken through the ±1.5% range of the last take-profit price and has stabilized for at least 2 5-minute K-lines after a retest.\n"
                    "2. The 5-minute K-line shows at least 3 strong solid K-lines in the same direction (body length > 1.5 times the average volatility).\n"
                    f"3. The RSI(6) maintains the same direction of deviation as at the time of the last take profit (e.g., {'RSI is still above 55 and rising for a long position' if order_history[-1]['side'] == 'BUY' else 'RSI is still below 45 and falling for a short position'}).\n"
                    "4. The MACD histogram has expanded its momentum in the same direction for 3 consecutive K-lines.\n"
                    "5. The trading volume has increased by more than 15% compared to the time of the last take profit.\n\n"
                    "If the standards are not met, you should wait at least 30 minutes after the last order to evaluate; even if the conditions are met, you should still judge whether it is the best time to enter the market based on the entry strategy and recent K-line information and technical indicators!\n\n"
                )

        template = self.get_prompt_for_new_order_template()
        prompt = PromptTemplate.from_template(template)
        
        parser = JsonOutputParser()
        
        chain = prompt | self.llm | parser

        return chain.invoke({
            "order_history_section": order_history_section,
            "no_order_reason_section": no_order_reason_section,
            "current_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "symbol": price_info['symbol'],
            "price": "{:.1f}".format(float(price_info['price'])),
            "kline_data": kline_data,
            "additional_conditions": additional_conditions
        })

    def get_prompt_for_stop_loss_or_take_profit(self, order_record):
        with open(self.reason_file, 'r') as f:
            reason = f.read()
        with open(self.modify_reason_file, 'r') as f:
            modify_reason = f.read()
        with open(self.file_path, 'r') as f:
            order_open_info = json.load(f)

        if order_record is None:
            return "Cannot load order record."

        # 使用新的工具函數獲取數據
        kline_info_5m = get_kline_5m(self.symbol)
        kline_info_15m = get_kline_15m(self.symbol)
        kline_info_1h = get_kline_1h(self.symbol)
        kline_info_4h = get_kline_4h(self.symbol)
        order_history = self._calculate_order_pnl()

        try:
            main_order = get_open_futures_orders(self.symbol)[0]
        except Exception as e:
            with open('error.txt','a',encoding='utf-8') as file:
                file.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n") 
                file.write(f"API Exception: {e}\n")
            print("Cannot get current order information!")
            return

        stop_loss_order = order_record.get('stop_loss_order')
        take_profit_order = order_record.get('take_profit_order')
        mark_price = float(main_order['markPrice'])
        break_even_price = float(main_order['breakEvenPrice'])
        unrealized_profit = float(main_order['unRealizedProfit'])
        notional = abs(float(main_order['notional']))
        leverage = 20
        pnl_rate = unrealized_profit * 100 / (notional / leverage)
        position_amt = abs(float(main_order['positionAmt']))
        side = 'BUY' if take_profit_order['stopPrice'] > stop_loss_order['stopPrice'] else 'SELL'

        reason_section = ""
        if reason:
            reason_section += (
                "### Core Logic at the Time of Opening (Must be verified if still valid):\n"
                f"- {reason}\n\n"
                "Please strictly check the following conditions:\n"
                "1. Does the current market still conform to the above opening logic?\n"
                "2. Are there any new technical indicators or events that overturn the original logic?\n"
                "3. If the original logic has not failed, please avoid adjusting the stop loss due to short-term fluctuations.\n\n"
            )
            confidence = int(order_open_info.get("trading_confidence_index"))
            reason_section += (
                f"### Opening Confidence Index ({confidence}/100):\n"
                "The higher the confidence index, the stronger the opening logic, and more tolerance for fluctuations should be given.\n"
                f"- If the confidence index is ≥80: A strong reversal signal is required to recommend a stop loss.\n"
                f"- If the confidence index is ≤50: It can be flexibly adjusted according to short-term indicators.\n\n"
            )
        if modify_reason:
            reason_section += f"The reason for modifying the order last time was:\n- {modify_reason}\n\n"

        order_history_section = ""
        if len(order_history) != 0:
            order_history_section += "Here is the recent historical order record:\n"
            for order in order_history[-5:]:
                order_history_section += (
                    f"  - Order Time: {order['update_time']}\n"
                    f"  - Close Time: {order['stop_time']}\n"
                    f"  - Order Side: {'Long' if order['side'] == 'BUY' else 'Short'}\n"
                    f"  - Open Price: {order['open_price']}\n"
                    f"  - Close Price: {order['stop_price']}\n"
                    f"  - PNL Percentage: {order['pnl_percentage']}%\n"
                    f"  - Net PNL Percentage: {order['net_pnl_percentage']}%\n\n"
                )
            order_history_section += "Hint: Optimize the current take-profit and stop-loss strategy based on historical operations.\n\n"

        net_profit = 0
        if side == 'BUY':
            net_profit = ((mark_price - break_even_price) * position_amt) * 100 / (notional / leverage)
        elif side == 'SELL':
            if mark_price < break_even_price:
                net_profit = ((break_even_price - mark_price) * position_amt) * 100 / (notional / leverage)
            else:
                net_profit = -((mark_price - break_even_price) * position_amt) * 100 / (notional / leverage)

        pnl_strategy_section = ""
        if abs(pnl_rate) < 1.0:
            pnl_strategy_section += (
                "### Micro-fluctuation Strategy:\n"
                "The current profit and loss fluctuation is less than 1%. The following conditions must be met at the same time to operate:\n"
                "1. The price has been consolidating near the cost price for more than 30 minutes.\n"
                "2. The volatility (ATR) has dropped to below 50% of that at the time of opening.\n"
                "Otherwise, the original stop-loss setting should be maintained, waiting for the trend to develop.\n\n"
            )
        elif pnl_rate >= 5 and net_profit <= 15:
            pnl_strategy_section += (
                "### Profit Strategy Adjustment (Relax take-profit conditions):\n"
                "When the floating profit is 5%~15%, at least two short-period K-line reversal signals are required to recommend taking profit.\n"
            )
        elif pnl_rate > 15:
            pnl_strategy_section += (
                "### Short-term Take-Profit and Stop-Loss Strategy When in Profit:\n"
                "1. When the profit exceeds 15%, the market has entered a more obvious profit stage. At this time, profits should be locked in quickly.\n"
                "   - Please quickly adjust the stop-loss price in a favorable direction according to the short-period K-line signal to ensure that at least 70% of the existing profit is locked in.\n"
                "   - If the short-period K-line shows a clear reversal signal or the trend slows down (such as a large trading volume accompanied by an extremely long shadow, engulfing pattern, or doji star), the position should be closed at the market price immediately.\n"
                "2. The goal is to protect profits in a timely manner and prevent the profit from shrinking or turning into a loss due to a short-term market reversal.\n"
                "### Goal:\n"
                "Quickly lock in profits and avoid profit retracement due to market reversal.\n\n"
            )
        elif pnl_rate < -4:
            pnl_strategy_section += (
                "### Short-term Take-Profit and Stop-Loss Strategy When in Loss:\n"
                "1. When the order is in a loss state, the loss should be strictly controlled, and the loss should be stopped immediately to prevent further expansion of the loss.\n"
                "2. Observe the short-period K-line and trading volume. If there are obvious signs of reversal, the position should be closed decisively to stop the loss.\n"
                "3. If the market trend is still in line with the thinking at the time of opening, it is forbidden to change the stop-loss price; but if the market trend is clearly reversed, the position should be closed immediately to stop the loss.\n"
                "### Goal:\n"
                "Analyze the latest market trends, control losses, and protect capital.\n\n"
            )
        else:
            pnl_strategy_section += (
                "### Current Take-Profit and Stop-Loss Strategy:\n"
                "1. The current profit is insufficient, and more room for fluctuation should be reserved to avoid premature stop-loss due to short-term fluctuations.\n"
                "2. Pay close attention to the changes in the short-period K-line. If there are preliminary reversal signals (such as a surge in trading volume accompanied by an extremely long shadow or a moving average crossover), the take-profit and stop-loss should be adjusted in a timely manner to protect existing profits or avoid expanding losses.\n"
                "3. If the market trend is clearly reversed, the position should be closed in a timely manner according to the short-period signal; otherwise, please hold the order as much as possible.\n"
                "4. If the market trend is still in line with the thinking at the time of opening, it is forbidden to change the stop-loss price to avoid premature stop-loss.\n"
                "### Goal:\n"
                "Hold the order as much as possible to maximize the return, unless the holding time is too long and the market trend may reverse, otherwise do not easily stop the loss at the market price.\n\n"
            )

        holding_time_section = ""
        time_diff = datetime.now() - datetime.fromtimestamp(main_order['updateTime'] / 1000)
        if time_diff < timedelta(minutes=30) and pnl_rate < 10:
            holding_time_section += (
                "### Holding Time Hint:\n"
                "The current order holding time is less than 30 minutes. If there is no strong reversal signal, please do not easily stop the loss.\n"
                "Short-term fluctuations are normal. You need to observe at least 3 5-minute K-lines before making a decision.\n\n"
            )

        reversal_conditions_section = (
            "### Conditions that can be regarded as reversal signals (at least two of the following must be met at the same time):\n"
            "1. The short-period K-line shows an engulfing pattern and the trading volume > the average of the previous 5 bars * 1.5.\n"
            "2. The RSI(6) fluctuates by more than 15 points within a single K-line.\n"
            "3. The MACD histogram expands in the opposite direction for 3 consecutive K-lines (the value difference > 50% of the previous value).\n"
            "4. The price breaks through the key level defined in the opening logic (e.g., the original support level turns into a resistance level).\n"
            "Please strictly check the above conditions before taking profit or stopping loss to avoid premature stop-loss due to short-term fluctuations.\n\n"
        )

        breakeven_stop_section = ""
        is_breakeven_stop = abs(float(stop_loss_order['stopPrice']) - float(main_order['breakEvenPrice'])) / float(main_order['breakEvenPrice']) <= 0.001
        if is_breakeven_stop:
            direction_desc = "below" if side == 'BUY' else "above"
            rsi_action = "breaks below 40 and continues to fall" if side == 'BUY' else "breaks above 60 and continues to rise"
            breakeven_stop_section += (
                "### Special Strategy When the Stop Loss is at the Breakeven Price:\n"
                "1. At least two of the following conditions must be met:\n"
                f"   - The closing price of 3 consecutive 5-minute K-lines is {direction_desc} the cost price.\n"
                f"   - The RSI(6) {rsi_action}.\n"
                "   - The trading volume has decreased by 50% compared to the time of opening and the open interest has decreased by 10%.\n"
                "2. If the holding time is <30 minutes, an additional condition must be met: the 4-hour trend breaks in the opposite direction.\n\n"
            )

        kline_data = ""
        kline_data += self.format_kline_data(kline_info_5m, "5分鐘")
        kline_data += self.format_kline_data(kline_info_15m, "15分鐘")
        kline_data += self.format_kline_data(kline_info_1h, "1小時")
        kline_data += self.format_kline_data(kline_info_4h, "4小時")

        pnl_prompt = ""
        if pnl_rate >= 10:
            pnl_prompt += f"\nHint: **The floating profit is {'{:.2f}'.format(net_profit)}%, you should gradually raise the stop loss to ensure at least {'{:.2f}'.format(net_profit*0.7)}% of the profit.**\n"
        elif pnl_rate >= 5:
            pnl_prompt += "\nHint: **The current profit exceeds 5%, please adjust the stop loss immediately to ensure at least 3-4% of the profit!**\n"

        order_info = (
            f"  - Open Time: {datetime.fromtimestamp(main_order['updateTime'] / 1000).strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"  - Order Side: {'Long' if take_profit_order['stopPrice'] > stop_loss_order['stopPrice'] else 'Short'}\n"
            f"  - Main Order Price: {main_order['entryPrice']}\n"
            f"  - Stop Loss Price: {stop_loss_order.get('stopPrice')}\n"
            f"  - Take Profit Price: {take_profit_order.get('stopPrice')}\n"
            f"  - Liquidation Price: {'{:.2f}'.format(float(main_order['liquidationPrice']))}\n"
            f"  - Breakeven Price: {'{:.2f}'.format(float(main_order['breakEvenPrice']))}\n"
            f"  - Net Profit: {'{:.2f}'.format(net_profit)}%\n"
            f"  - Current PNL: {'{:.2f}'.format(pnl_rate)}%\n"
        )

        important_reminder = ""
        if pnl_rate > 15:
            important_reminder += (            
            "\n**Important Reminder**:\n"
            "If you think the profit has reached the target, please choose to take profit at the market price instead of adjusting the take-profit and stop-loss.\n"
            "The market is changing rapidly, and excessive waiting may lead to profit shrinkage.\n"
            "If you think the market trend is about to reverse, you should **take profit immediately instead of waiting for a higher point.**\n"
            )

        template = self.get_prompt_for_stop_loss_or_take_profit_template()
        prompt = PromptTemplate.from_template(template)

        parser = JsonOutputParser()

        chain = prompt | self.llm | parser

        return chain.invoke({
            "reason_section": reason_section,
            "order_history_section": order_history_section,
            "pnl_strategy_section": pnl_strategy_section,
            "holding_time_section": holding_time_section,
            "reversal_conditions_section": reversal_conditions_section,
            "breakeven_stop_section": breakeven_stop_section,
            "current_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "symbol": main_order['symbol'],
            "price": "{:.1f}".format(float(main_order['markPrice'])),
            "kline_data": kline_data,
            "pnl_prompt": pnl_prompt,
            "order_info": order_info,
            "important_reminder": important_reminder
        })

    def get_prompt_for_new_order_template(self):
        return """
        You are a short-term swing trader focused on capturing signals from short-period K-lines (e.g., 5-minute, 15-minute), while using 1-hour and 4-hour K-lines for auxiliary purposes like determining holding periods and risk management. Your goal is to quickly capture profit opportunities in short time cycles and exit in a timely manner amidst short-term market fluctuations, avoiding excessive fees and expanded risks from frequent trading.

        **Notes:**
        1. Orders will be placed with 20x leverage. Please enter the market at the most optimal price to avoid excessive fees and risk amplification from high leverage.
        2. Entry points are primarily based on the patterns and volume changes of short-period K-lines (e.g., 5-minute, 15-minute), supplemented by a comprehensive judgment of technical indicators like RSI, MACD, ATR, engulfing patterns, and doji stars.
        3. Longer-period K-lines (1-hour, 4-hour) are only used to confirm the broader trend and overall market direction but do not restrict entry decisions for short-term trades.
        4. Averaging down or partial profit-taking/stop-loss is not supported. Ensure the risk of the opening position is manageable at the time of opening.
        5. If the market is in a consolidation phase, simply go short at resistance levels and long at support levels.

        **Entry Strategy:**
        - Consider going long at support levels and short at resistance levels.
        - If a bearish trend is dominant and there are signs of a short-term rebound in technical indicators or K-line patterns, **wait patiently until the rebound ends before shorting**. Conversely, if a bullish trend is dominant but there are signs of a short-term pullback, **wait patiently until the pullback is complete before going long**.
        - If a recent short-period K-line shows a long upper shadow (shadow length is about twice the body), and the price pulls back to the low of the previous K-line, it may indicate that selling pressure is active in the short term; consider shorting.
        - If a recent short-period K-line shows a long lower shadow (shadow length is about twice the body), and the price rallies to the high of the previous K-line, it may indicate that buying pressure is actively intervening in the short term; consider going long.
        - When the market shows a short-term pullback or rebound with shrinking volume, trade based on short-period K-line signals and flexibly adjust the holding period based on longer-period data.
        - When short-term moving averages (e.g., 5-minute, 15-minute) form a golden cross or death cross, this can also be used as an entry reference signal.
        - If the market shows strong momentum signals (e.g., RSI quickly breaks through overbought/oversold zones or MACD forms a clear crossover), corresponding actions can be taken (e.g., go long when a MACD golden cross forms; go short when a death cross forms).
        - Unless there are very clear market reversal signals (e.g., a large trading volume accompanied by extremely long upper/lower shadows, engulfing patterns, or doji stars), entry must be based on short-period K-line signals.

        Please analyze the current short-term market trend, K-line data of various time cycles, and technical indicators based on the above principles, and open an order at the best point. If the best point is missed, remain on the sidelines to avoid losses.
        
        {order_history_section}
        {no_order_reason_section}

        **Current Market Information:**
        - Current Time: {current_time}
        - Current Price: {symbol} = {price}

        {kline_data}

        {additional_conditions}

        Please analyze the above market information and answer the following questions:
        1. Is there a suitable trading opportunity currently?
        2. If a trading opportunity exists, please provide the recommended buy or sell direction, price range, and corresponding stop-loss and take-profit prices.
        3. Please briefly describe the reasons and logic behind your trading recommendation.

        Please reply in the following format strictly:
        {{"trading_opportunity": "Yes/No", "action": "BUY/SELL/null", "recommended_price": price, "confidence_index": 0-100, "stop_loss_price": price, "take_profit_price": price, "trading_reason": "Reason description, and provide suggestions for subsequent take-profit and stop-loss operations!"}}
        """

    def get_prompt_for_stop_loss_or_take_profit_template(self):
        return """
        You are a short-term swing trader who excels at making precise take-profit and stop-loss decisions using K-line data and technical indicators. Currently, you need to dynamically adjust the take-profit and stop-loss for an open order based on the latest market conditions to protect profits or control losses. You will receive market information every 150 seconds and react quickly based on changes in short-period K-lines (5-minute, 15-minute); while 1-hour and 4-hour K-lines are only for auxiliary purposes, mainly for adjusting the holding period, not as the primary basis for dynamic take-profit and stop-loss decisions. Note: This order is already open. If there is no clear reversal trend, **try to hold the order** until the take-profit price is reached to avoid paying high fees for repeated trades in a short period.

        **Important Matters:**
        1. Currently using 20x leverage, the market volatility risk is high. Please strictly adjust the take-profit and stop-loss according to short-period K-line signals to protect existing profits or stop losses in a timely manner.
        2. Focus on price fluctuations and volume changes in short-period K-lines; for example:
           - In a long order, if a short-period K-line shows a large trading volume accompanied by an extremely long upper shadow (pin bar reversal signal), it indicates that the market may reverse and fall rapidly. The stop-loss should be raised in advance or consider closing the position.
           - In a short order, if a short-period K-line shows a large trading volume accompanied by an extremely long lower shadow, it may predict a market reversal to the upside. The stop-loss should be raised in advance or the position should be closed to protect profits.
        3. Other short-term strategy references:
           - When short-term moving averages (5-minute, 15-minute) form a golden cross or death cross, or momentum indicators such as RSI and MACD show rapid changes, this should be used as a reference for adjusting take-profit and stop-loss.
           - If the market price breaks through short-term resistance or support levels and is subsequently confirmed by a retest, the take-profit and stop-loss should also be adjusted in a timely manner to lock in profits.
        4. Longer-period K-lines (1-hour, 4-hour) are only for auxiliary use, mainly to determine the overall market trend and holding period; even if short-term signals slightly deviate from the longer-period trend, it does not affect the adjustment of take-profit and stop-loss based on short-period data, but it must be noted that the holding period may be shorter.
        5. If the market shows clear reversal signals in the short term (e.g., a large trading volume accompanied by extremely long upper/lower shadows, engulfing patterns, or doji stars), and the order is still in a profitable state, priority should be given to protecting existing profits. Do not let profits shrink or turn into losses due to excessive waiting.
        6. If the market is in a consolidation phase, simply take profit on long orders at resistance levels or on short orders at support levels.
        7. The holding time of the order is also an important consideration. If the holding time is short (within 15 minutes) and the market does not show a clear reversal trend, please relax the stop-loss and give the order more chances; conversely, if the holding time is long, you should consider taking profit or stopping the loss.

        **Dynamic Take-Profit and Stop-Loss Basic Strategy:**
          - When long:
              If the price pulls back in the short term and shows clear reversal signals (e.g., a particularly long upper shadow accompanied by an increase in volume), the stop-loss point should be raised in advance, or the position should be closed at the market price to take profit, to avoid profit retracement.
          - When short:
              If the price rebounds in the short term and shows clear reversal signals (e.g., a particularly long lower shadow accompanied by an increase in volume), the stop-loss point should be raised in advance (lower the stop-loss price), or the position should be closed at the market price to take profit.
        Please provide specific dynamic take-profit and stop-loss operation suggestions based on the above principles, comprehensively analyzing the current market's short-term K-line data, price-volume relationships, and technical indicators, to protect existing profits or control losses.

        {reason_section}
        {order_history_section}
        {pnl_strategy_section}
        {holding_time_section}
        {reversal_conditions_section}
        {breakeven_stop_section}

        - Current Time: {current_time}
        - Current Price: {symbol} = {price}

        {kline_data}

        {pnl_prompt}

        **Current Order Information:**
        {order_info}

        **Order Information Name Tips:**
        1. Cost Price (can be used as a reference price for stop-loss): Represents the actual cost price after considering transaction costs such as fees when calculating profit and loss, the price at which the profit and loss is zero.
        2. Net Profit: The actual profit percentage calculated based on the order, the net profit after deducting relevant costs.

        {important_reminder}

        Please reply strictly in the following format:
        {{"action": "STOP_LOSS_TAKE_PROFIT", "reason": "Reason for choosing to take profit/stop loss immediately"}}
        
        If you think you need to modify the take-profit and stop-loss settings, please reply strictly in the following format:
        {{"action": "MODIFY_STOP_LOSS_TAKE_PROFIT", "stop_loss_price": price, "take_profit_price": price, "reason": "Analyze the market situation and give reasons for the modification"}}
        
        If you think the take-profit and stop-loss settings are reasonable, please reply strictly in the following format:
        {{"action": null, "reason": "Brief analysis"}}
        """