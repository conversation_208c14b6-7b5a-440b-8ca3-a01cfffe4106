"""
簡化版增強主程序
不使用調度器，手動執行交易分析
"""

# 添加路徑以支持模組導入
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime
import time
import traceback
from typing import Optional, Dict, Any

from agents.advanced_langchain_agent import AdvancedTradingAgent
from core.binance_api import BinanceAPI
import dotenv
dotenv.load_dotenv()


class SimpleTradingBot:
    """簡化版交易機器人"""
    
    def __init__(self, symbol: str = 'ETHUSDT', model_name: str = "gemini-2.5-flash-lite-preview-06-17"):
        self.symbol = symbol
        self.model_name = model_name
        
        # 初始化高級代理
        self.agent = AdvancedTradingAgent(
            model_name=model_name,
            symbol=symbol,
            memory_type="buffer_summary"
        )
        
        # 初始化傳統 API（用於狀態檢查）
        self.binance = BinanceAPI(symbol=symbol)
        
        # 狀態追蹤
        self.last_analysis_time = None
        self.consecutive_errors = 0
        self.max_consecutive_errors = 3
        
        print(f"🚀 簡化版交易機器人已初始化")
        print(f"📊 交易對: {symbol}")
        print(f"🤖 模型: {model_name}")
        print(f"💾 記憶系統: 已啟用")
        print("-" * 50)
    
    def get_current_trading_status(self) -> Dict[str, Any]:
        """獲取當前交易狀態"""
        try:
            orders = self.binance.get_trading_status()
            main_orders = self.binance.get_open_futures_orders()
            
            status = {
                'has_position': False,
                'order_count': len(orders),
                'main_order_count': len(main_orders),
                'orders': orders,
                'main_orders': main_orders,
                'timestamp': datetime.now().isoformat()
            }
            
            # 判斷是否有持倉
            if len(orders) == 2 and len(main_orders) != 0:
                status['has_position'] = True
                status['main_order'] = main_orders[0]
                
                # 分類止損止盈訂單
                for order in orders:
                    if order.get("type") == "STOP_MARKET":
                        status['stop_loss_order'] = order
                    elif order.get("type") == "TAKE_PROFIT_MARKET":
                        status['take_profit_order'] = order
            
            return status
            
        except Exception as e:
            return {
                'error': f"獲取交易狀態失敗: {str(e)}",
                'timestamp': datetime.now().isoformat()
            }
    
    def execute_trading_cycle(self):
        """執行交易週期"""
        try:
            print(f"\n🔄 開始交易分析週期 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 獲取當前交易狀態
            trading_status = self.get_current_trading_status()
            
            if 'error' in trading_status:
                print(f"❌ 狀態檢查失敗: {trading_status['error']}")
                self.consecutive_errors += 1
                return
            
            # 根據狀態決定分析類型
            if trading_status['has_position']:
                print("📊 檢測到持倉，執行持倉管理分析...")
                result = self.agent.execute_trading_analysis("position_management")
            else:
                print("🔍 無持倉，執行新交易機會分析...")
                result = self.agent.execute_trading_analysis("new_order")
            
            # 輸出結果
            print(f"🤖 AI 分析結果:")
            print("-" * 40)
            print(result)
            print("-" * 40)
            
            # 重置錯誤計數
            self.consecutive_errors = 0
            self.last_analysis_time = datetime.now()
            
            # 顯示記憶總結
            memory_summary = self.agent.get_memory_summary()
            if memory_summary and memory_summary != "無歷史決策記錄":
                print(f"\n💭 記憶總結:")
                print(memory_summary[:300] + "..." if len(memory_summary) > 300 else memory_summary)
            
        except Exception as e:
            print(f"❌ 交易週期執行失敗: {str(e)}")
            print(f"📋 錯誤詳情: {traceback.format_exc()}")
            self.consecutive_errors += 1
    
    def run_continuous(self, interval_seconds: int = 150, max_cycles: int = None):
        """連續運行交易分析"""
        print(f"🎯 開始連續交易分析，間隔: {interval_seconds} 秒")
        if max_cycles:
            print(f"📊 最大週期數: {max_cycles}")
        
        cycle_count = 0
        
        try:
            while True:
                # 執行交易週期
                self.execute_trading_cycle()
                cycle_count += 1
                
                # 檢查是否達到最大週期數
                if max_cycles and cycle_count >= max_cycles:
                    print(f"✅ 已完成 {max_cycles} 個週期，程序結束")
                    break
                
                # 檢查連續錯誤
                if self.consecutive_errors >= self.max_consecutive_errors:
                    print(f"⚠️ 連續錯誤 {self.consecutive_errors} 次，暫停 5 分鐘...")
                    time.sleep(300)  # 暫停 5 分鐘
                    self.consecutive_errors = 0
                
                # 等待下一個週期
                print(f"\n⏰ 等待 {interval_seconds} 秒到下一個週期...")
                time.sleep(interval_seconds)
                
        except KeyboardInterrupt:
            print("\n🛑 收到停止信號，正在關閉...")
            print("✅ 交易機器人已安全關閉")


def main():
    """主函數"""
    print("🚀 啟動簡化版 LangChain 交易機器人")
    print("=" * 60)
    
    try:
        # 創建交易機器人
        bot = SimpleTradingBot(
            symbol='ETHUSDT',
            model_name="gemini-2.5-flash-lite-preview-06-17"
        )
        
        # 詢問運行模式
        print("\n選擇運行模式:")
        print("1. 單次分析")
        print("2. 連續運行 (3次)")
        print("3. 連續運行 (無限)")
        
        choice = input("請選擇 (1-3): ").strip()
        
        if choice == "1":
            bot.execute_trading_cycle()
        elif choice == "2":
            bot.run_continuous(interval_seconds=150, max_cycles=3)
        elif choice == "3":
            bot.run_continuous(interval_seconds=150)
        else:
            print("無效選擇，執行單次分析")
            bot.execute_trading_cycle()
        
    except Exception as e:
        print(f"❌ 啟動失敗: {str(e)}")
        print(f"📋 錯誤詳情: {traceback.format_exc()}")


if __name__ == '__main__':
    main()
